package oryx.utility.render.animation

import oryx.utility.misc.Timer

class Animation(var ease: Ease, var duration: Long) {
    private val timer = Timer()

    var start = 0F
    var value = 0F
    var target = 0F

    val progress
        get() = timer.elapsedTime / duration.toFloat()

    var finished = false
        private set
    
    fun run(destination: Float) {
        if (target != destination) {
            target = destination
            reset()
        } else {
            finished = timer.finished(duration)
            if (finished) {
                value = destination
                return
            }
        }

        val result = ease.easing(progress)
        value = if (value > destination)
            start - (start - destination) * result
            else start + (destination - start) * result
    }
    
    fun reset() {
        timer.reset()
        start = value
        finished = false
    }
}