package oryx.utility.player

import org.lwjgl.input.Mouse
import oryx.utility.math.lerp
import oryx.utility.misc.Timer
import oryx.utility.render.animation.Animation
import oryx.utility.render.animation.Ease

class ScrollUtility {
    private val animation = Animation(Ease.IN_OUT_CUBIC, 500L)

    private val scrollTimer = Timer()

    private val stretch = 30F
    var scroll = 0F
        private set
    var target = 0F
        private set
    var max = 0F

    init {
        reset()
    }

    fun update() {
        if (scrollTimer.finished(70L)) {
            val wheel = -Mouse.getDWheel() * stretch

            target = if (wheel != 0F) {
                Math.clamp(target + wheel, stretch, max - stretch)
            } else {
                Math.clamp(target, 0F, max)
            }

            scrollTimer.reset()
        }

        scroll = lerp(scroll, target, 0.2F)
    }

    fun reset() {
        scroll = 0F
        target = 0F
        scrollTimer.reset()
    }
}