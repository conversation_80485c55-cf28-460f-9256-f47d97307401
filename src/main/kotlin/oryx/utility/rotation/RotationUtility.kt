package oryx.utility.rotation

import net.minecraft.entity.Entity
import net.minecraft.util.MathHelper
import net.minecraft.util.Vec3
import oryx.utility.extension.coerceIn
import oryx.utility.extension.toDegrees
import oryx.utility.misc.mc
import oryx.utility.misc.player
import kotlin.math.*

val gcd: Float
    get() {
        val sensitivity = mc.options.mouseSensitivity * 0.6F + 0.2F
        val gcd = sensitivity.pow(3) * 8F
        return gcd * 0.15F
    }

fun distance(targetRotation: Rotation, lastRotation: Rotation) = hypot(
    abs(MathHelper.wrapDegrees(targetRotation.yaw - lastRotation.yaw)),
    abs(targetRotation.pitch - lastRotation.pitch)
)

fun rotationDifference(targetRotation: Rotation, lastRotation: Rotation): Rotation {
    var yawDiff = MathHelper.wrapDegrees(targetRotation.yaw - lastRotation.yaw)
    var pitchDiff = MathHelper.wrapDegrees(targetRotation.pitch - lastRotation.pitch)

    if (abs(yawDiff) > 180F) yawDiff += 360F
    if (abs(pitchDiff) > 180F) pitchDiff += 360F

    return Rotation(yawDiff, pitchDiff)
}

fun closetRotation(entity: Entity): Rotation {
    val eyePos = player.getPositionEyes(1F)
    val box = entity.entityBoundingBox

    val points = arrayOf(
        Vec3((box.minX + box.maxX) / 2, box.minY, (box.minZ + box.maxZ) / 2),
        Vec3((box.minX + box.maxX) / 2, (box.minY + box.maxY) / 2, (box.minZ + box.maxZ) / 2),
        Vec3((box.minX + box.maxX) / 2, box.maxY - 0.15, (box.minZ + box.maxZ) / 2)
    )

    val closestPoint = points.minBy { it.squareDistanceTo(eyePos) }

    return rotateTo(closestPoint)
}

fun rotateTo(targetVec: Vec3): Rotation {
    val playerEyePos = player.getPositionEyes(1F)
    val deltaX = targetVec.x - playerEyePos.x
    val deltaY = targetVec.y - playerEyePos.y
    val deltaZ = targetVec.z - playerEyePos.z

    val distance = hypot(deltaX, deltaZ)

    val yaw = atan2(deltaZ, deltaX).toDegrees() - 90
    val pitch = -atan2(deltaY, distance).toDegrees()

    return Rotation(yaw.toFloat(), pitch.toFloat())
}

fun closetRotation2(entity: Entity): Rotation {
    val hitVec = findHitVec(entity)
    return rotateTo(hitVec)
}

fun findHitVec(entity: Entity): Vec3 {
    val eyesPos = player.getPositionEyes(1F)

    val collisionBorderSize = entity.collisionBorderSize.toDouble()

    val entityBB = entity.entityBoundingBox.expand(collisionBorderSize)

    return eyesPos.coerceIn(entityBB)
}

fun smoothGCD(targetRotation: Rotation, lastRotation: Rotation, baseSpeed: Float = 40F): Rotation {
    val rotationDiff = rotationDifference(targetRotation, lastRotation)

    var yawDiff = rotationDiff.yaw
    var pitchDiff = rotationDiff.pitch

    val totalDistance = sqrt(yawDiff * yawDiff + pitchDiff * pitchDiff)

    val distanceMultiplier = when {
        totalDistance > 90F -> 0.8F
        totalDistance > 30F -> 0.6F
        totalDistance > 10F -> 0.4F
        else -> 0.2F
    }

    val adaptiveSpeed = baseSpeed * distanceMultiplier

    val smoothingFactor = min(1F, adaptiveSpeed / 100F)
    val exponentialFactor = 1F - exp(-smoothingFactor * 3F)

    yawDiff *= exponentialFactor
    pitchDiff *= exponentialFactor

    val smoothedRotation = Rotation(
        lastRotation.yaw + yawDiff,
        lastRotation.pitch + pitchDiff
    )

    return advancedGCDFix(smoothedRotation, lastRotation)
}

fun advancedGCDFix(rotation: Rotation, lastRotation: Rotation): Rotation {
    val deltaYaw = rotation.yaw - lastRotation.yaw
    val deltaPitch = rotation.pitch - lastRotation.pitch

    val yawGCD = intelligentGCDRound(deltaYaw)
    val pitchGCD = intelligentGCDRound(deltaPitch)

    val fixedYaw = (lastRotation.yaw + yawGCD).toFloat()
    val fixedPitch = (lastRotation.pitch + pitchGCD).toFloat()

    return Rotation(fixedYaw, fixedPitch)
}

private fun intelligentGCDRound(delta: Float): Double {
    if (abs(delta) < 0.001) return 0.0

    val gcdValue = gcd
    val ratio = delta / gcdValue

    val roundedRatio = when {
        abs(ratio) < 0.1 -> 0.0
        abs(ratio) < 0.9 -> ratio
        else -> ratio.roundToInt().toDouble()
    }

    return roundedRatio * gcdValue
}
