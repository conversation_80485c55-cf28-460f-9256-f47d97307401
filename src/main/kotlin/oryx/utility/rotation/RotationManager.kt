package oryx.utility.rotation

import oryx.event.base.Listen
import oryx.event.base.Listenable
import oryx.event.base.Priority
import oryx.event.impl.*
import oryx.utility.misc.mc
import oryx.utility.misc.player
import oryx.utility.misc.realRotation
import oryx.utility.player.fixMove

object RotationManager : Listenable {
    private var currentRotation = Rotation()
    private var targetRotation = Rotation()
    private var keepLength = 0

    private var moveFix = true
    private var rotating = false

    init {
        register()
    }

    fun rotateTo(rotation: Rotation, keepLength: Int = 1, moveFix: Boolean = true) {
        if (currentRotation.isEmpty()) currentRotation = realRotation
        
        targetRotation = rotation
        this.keepLength = keepLength
        this.moveFix = moveFix
        rotating = true
    }

    @Listen(Priority.LOWEST)
    private fun onLook(event: LookEvent) {
        if (rotating && currentRotation.isNotEmpty()) {
            event.yaw = currentRotation.yaw
            event.pitch = currentRotation.pitch
        }
    }

    @Listen(Priority.LOWEST)
    private fun onTick(event: TickEvent) {
        if (rotating) {
            keepLength--
            if (keepLength <= 0) rotating = false
        }
    }

    @Listen(Priority.LOWEST)
    private fun onUpdate(event: UpdateEvent) {
        if (targetRotation.isNotEmpty() && currentRotation.isNotEmpty()) {
            // Apply smoothing to move toward target rotation
            currentRotation = smoothGCD(targetRotation, currentRotation)
            
            // Check if we've reached the target rotation
            val distance = distance(targetRotation, currentRotation)
            if (distance < 1) currentRotation = targetRotation
        }
    }

    @Listen(Priority.LOWEST)
    private fun onPreMotion(event: PreMotionEvent) {
        if (rotating && currentRotation.isNotEmpty()) {
            event.yaw = currentRotation.yaw
            event.pitch = currentRotation.pitch

            player.headYaw = currentRotation.yaw
            player.renderYawOffset = currentRotation.yaw
            player.headPitch = currentRotation.pitch
            //realRotation = currentRotation
        }
    }

    @Listen(Priority.LOWEST)
    private fun onStrafe(event: StrafeEvent) {
        if (rotating && moveFix && currentRotation.isNotEmpty()) {
            event.yaw = currentRotation.yaw
        }
    }

    @Listen(Priority.LOWEST)
    private fun onJump(event: JumpEvent) {
        if (rotating && moveFix && currentRotation.isNotEmpty()) {
            event.yaw = currentRotation.yaw
        }
    }

    @Listen(Priority.LOWEST)
    private fun onPostMotion(event: PostMotionEvent) {
        // If we're no longer actively rotating and current rotation matches player rotation, reset
        if (!rotating && currentRotation.isNotEmpty()) {
            val distance = distance(currentRotation, realRotation)
            
            if (distance < 1) reset()
        }
    }

    @Listen(Priority.LOW)
    private fun onMoveInput(event: MoveInputEvent) {
        if (rotating && moveFix && currentRotation.isNotEmpty()) {
            fixMove(event, currentRotation.yaw)
        }
    }

    private fun reset() {
        keepLength = 0
        rotating = false
        currentRotation = Rotation()
        targetRotation = Rotation()
    }
}