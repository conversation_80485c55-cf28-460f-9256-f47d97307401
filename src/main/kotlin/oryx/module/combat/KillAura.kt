package oryx.module.combat

import net.minecraft.entity.EntityLivingBase
import org.lwjgl.input.Keyboard
import oryx.event.base.Listen
import oryx.event.impl.UpdateEvent
import oryx.module.base.Module
import oryx.utility.misc.mc
import oryx.utility.misc.player
import oryx.utility.misc.world
import oryx.utility.rotation.RotationManager
import oryx.utility.rotation.closetRotation2

object KillAura : Module() {
    init {
        key = Keyboard.KEY_R
    }

    @Listen
    fun onUpdate(event: UpdateEvent) {
        val targets = world.loadedEntityList
        targets.sortBy { player.getDistanceToEntity(it) }
        val target = targets.firstOrNull { it != player && it is EntityLivingBase }

        if (target != null &&
            target.isEntityAlive &&
            player.canEntityBeSeen(target) &&
            player.getDistanceToEntity(target) <= 8
        ) {
            val rotation = closetRotation2(target)
            RotationManager.rotateTo(rotation, 3, true)

            if (player.getDistanceToEntity(target) < 5 && target.hurtResistantTime < 15) {
                mc.clickMouse()
            }
        }
    }
}